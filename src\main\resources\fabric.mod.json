{"schemaVersion": 1, "id": "near-mod", "version": "${version}", "name": "Near Mod", "description": "Adds /near command to show all players within 128 blocks, including invisible ones", "authors": ["NearMod Developer"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/near-mod/icon.png", "environment": "client", "entrypoints": {"client": ["com.example.near.NearMod"]}, "mixins": ["near-mod.mixins.json"], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21.5", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}