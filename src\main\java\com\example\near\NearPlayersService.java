package com.example.near;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for finding nearby players
 */
public class NearPlayersService {
    private static final double SEARCH_RADIUS = 128.0;
    
    /**
     * Finds all players within the search radius of the current player
     */
    public static List<PlayerInfo> findNearbyPlayers() {
        MinecraftClient client = MinecraftClient.getInstance();
        
        // Check if we're in a world and have a player
        if (client.player == null || client.world == null) {
            return new ArrayList<>();
        }
        
        ClientPlayerEntity player = client.player;
        Vec3d playerPos = player.getPos();
        World world = player.getWorld();
        
        return findNearbyPlayers(world, playerPos, player);
    }
    
    /**
     * Finds all players within the search radius of a specific position
     */
    public static List<PlayerInfo> findNearbyPlayers(World world, Vec3d centerPos, ClientPlayerEntity excludePlayer) {
        List<PlayerInfo> nearbyPlayers = new ArrayList<>();
        
        // Get all players in the client world
        List<? extends PlayerEntity> allPlayers = world.getPlayers();
        
        for (PlayerEntity otherPlayer : allPlayers) {
            // Skip the player who is the center of the search
            if (otherPlayer.equals(excludePlayer)) {
                continue;
            }
            
            Vec3d otherPos = otherPlayer.getPos();
            double distance = centerPos.distanceTo(otherPos);
            
            // Check if player is within radius
            if (distance <= SEARCH_RADIUS) {
                boolean isInvisible = otherPlayer.isInvisible();
                
                PlayerInfo info = new PlayerInfo(
                    otherPlayer.getName().getString(),
                    distance,
                    otherPos.x,
                    otherPos.y,
                    otherPos.z,
                    isInvisible
                );
                
                nearbyPlayers.add(info);
            }
        }
        
        // Sort by distance (closest first)
        nearbyPlayers.sort((a, b) -> Double.compare(a.distance, b.distance));
        
        return nearbyPlayers;
    }
    
    /**
     * Gets the search radius
     */
    public static double getSearchRadius() {
        return SEARCH_RADIUS;
    }
}
