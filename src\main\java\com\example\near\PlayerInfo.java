package com.example.near;

/**
 * Represents information about a nearby player
 */
public class PlayerInfo {
    public final String name;
    public final double distance;
    public final double x, y, z;
    public final boolean isInvisible;
    
    public PlayerInfo(String name, double distance, double x, double y, double z, boolean isInvisible) {
        this.name = name;
        this.distance = distance;
        this.x = x;
        this.y = y;
        this.z = z;
        this.isInvisible = isInvisible;
    }
    
    /**
     * Formats the player information for display
     */
    public String getDisplayText() {
        return String.format("%s - %.1fm%s", 
            name, 
            distance, 
            isInvisible ? " [Invisible]" : "");
    }
    
    /**
     * Gets detailed information including coordinates
     */
    public String getDetailedText() {
        return String.format("%s - %.1fm (%d, %d, %d)%s",
            name,
            distance,
            (int)x,
            (int)y,
            (int)z,
            isInvisible ? " [Invisible]" : "");
    }
}
