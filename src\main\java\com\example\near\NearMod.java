package com.example.near;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NearMod implements ClientModInitializer {
	public static final String MOD_ID = "near-mod";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	// Key bindings for toggling overlay features
	public static KeyBinding toggleOverlayKey;
	public static KeyBinding toggleCoordinatesKey;

	@Override
	public void onInitializeClient() {
		// This code runs as soon as Minecraft client is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.

		LOGGER.info("Near Mod (Client) initialized!");

		// Initialize the overlay
		NearPlayersOverlay.initialize();

		// Initialize key input handler
		KeyInputHandler.initialize();

		// Register key bindings
		toggleOverlayKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.near-mod.toggle_overlay",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_N,
			"category.near-mod"
		));

		toggleCoordinatesKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.near-mod.toggle_coordinates",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_M,
			"category.near-mod"
		));

		LOGGER.info("Near Mod overlay and key bindings registered!");
	}
}