package com.example.near;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.AbstractClientPlayerEntity;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

import static net.fabricmc.fabric.api.client.command.v2.ClientCommandManager.literal;

public class NearCommand {
    private static final double SEARCH_RADIUS = 128.0;

    public static void register(CommandDispatcher<FabricClientCommandSource> dispatcher, CommandRegistryAccess registryAccess) {
        dispatcher.register(literal("near")
                .executes(NearCommand::execute));
    }

    private static int execute(CommandContext<FabricClientCommandSource> context) {
        FabricClientCommandSource source = context.getSource();
        MinecraftClient client = MinecraftClient.getInstance();

        try {
            // Get the client player
            ClientPlayerEntity player = client.player;
            if (player == null) {
                source.sendError(Text.literal("§cPlayer not found!"));
                return 0;
            }

            Vec3d playerPos = player.getPos();
            World world = player.getWorld();

            // Find all players within radius
            List<PlayerInfo> nearbyPlayers = findNearbyPlayers(world, playerPos, player);

            if (nearbyPlayers.isEmpty()) {
                source.sendFeedback(Text.literal("§eNo players found within " + (int)SEARCH_RADIUS + " blocks."));
                return 1;
            }

            // Send header message
            source.sendFeedback(Text.literal("§6=== Players within " + (int)SEARCH_RADIUS + " blocks ==="));

            // Send player list
            for (PlayerInfo info : nearbyPlayers) {
                String message = String.format("§a%s §7- §e%.1f blocks §7(§f%d, %d, %d§7)%s",
                        info.name,
                        info.distance,
                        (int)info.x,
                        (int)info.y,
                        (int)info.z,
                        info.isInvisible ? " §8[Invisible]" : "");

                source.sendFeedback(Text.literal(message));
            }

            source.sendFeedback(Text.literal("§6Total: §e" + nearbyPlayers.size() + " §6players"));

        } catch (Exception e) {
            source.sendError(Text.literal("§cError executing command: " + e.getMessage()));
            NearMod.LOGGER.error("Error in /near command", e);
            return 0;
        }

        return 1;
    }
    
    private static List<PlayerInfo> findNearbyPlayers(World world, Vec3d centerPos, ClientPlayerEntity excludePlayer) {
        List<PlayerInfo> nearbyPlayers = new ArrayList<>();

        // Get all players in the client world
        List<? extends PlayerEntity> allPlayers = world.getPlayers();

        for (PlayerEntity otherPlayer : allPlayers) {
            // Skip the player who executed the command
            if (otherPlayer.equals(excludePlayer)) {
                continue;
            }

            Vec3d otherPos = otherPlayer.getPos();
            double distance = centerPos.distanceTo(otherPos);

            // Check if player is within radius
            if (distance <= SEARCH_RADIUS) {
                boolean isInvisible = otherPlayer.isInvisible();

                PlayerInfo info = new PlayerInfo(
                        otherPlayer.getName().getString(),
                        distance,
                        otherPos.x,
                        otherPos.y,
                        otherPos.z,
                        isInvisible
                );

                nearbyPlayers.add(info);
            }
        }

        // Sort by distance
        nearbyPlayers.sort((a, b) -> Double.compare(a.distance, b.distance));

        return nearbyPlayers;
    }
    
    private static class PlayerInfo {
        final String name;
        final double distance;
        final double x, y, z;
        final boolean isInvisible;
        
        PlayerInfo(String name, double distance, double x, double y, double z, boolean isInvisible) {
            this.name = name;
            this.distance = distance;
            this.x = x;
            this.y = y;
            this.z = z;
            this.isInvisible = isInvisible;
        }
    }
}
