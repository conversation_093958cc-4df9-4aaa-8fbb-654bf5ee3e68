package com.example.near;

import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.render.RenderTickCounter;

import java.util.ArrayList;
import java.util.List;

/**
 * Overlay that displays nearby players in the top-right corner of the screen
 */
public class NearPlayersOverlay {
    private static List<PlayerInfo> cachedNearbyPlayers = new ArrayList<>();
    private static int tickCounter = 0;
    
    /**
     * Initializes the overlay by registering the HUD render callback
     */
    public static void initialize() {
        HudRenderCallback.EVENT.register(NearPlayersOverlay::onHudRender);
    }
    
    /**
     * Called every frame to render the overlay
     */
    private static void onHudRender(DrawContext drawContext, RenderTickCounter tickCounter) {
        MinecraftClient client = MinecraftClient.getInstance();
        
        // Don't render if overlay is disabled or we're not in game
        if (!NearModConfig.overlayEnabled || client.player == null || client.world == null) {
            return;
        }
        
        // Don't render if debug screen is open or chat is open
        if (client.getDebugHud().shouldShowDebugHud() || client.inGameHud.getChatHud().isChatFocused()) {
            return;
        }
        
        // Update player list periodically
        NearPlayersOverlay.tickCounter++;
        if (NearPlayersOverlay.tickCounter >= NearModConfig.updateFrequency) {
            updateNearbyPlayers();
            NearPlayersOverlay.tickCounter = 0;
        }
        
        // Render the overlay
        renderOverlay(drawContext, client);
    }
    
    /**
     * Updates the cached list of nearby players
     */
    private static void updateNearbyPlayers() {
        try {
            List<PlayerInfo> allNearbyPlayers = NearPlayersService.findNearbyPlayers();
            
            // Filter out invisible players if configured
            if (!NearModConfig.showInvisiblePlayers) {
                allNearbyPlayers = allNearbyPlayers.stream()
                    .filter(player -> !player.isInvisible)
                    .toList();
            }
            
            // Limit the number of players shown
            if (allNearbyPlayers.size() > NearModConfig.maxPlayersShown) {
                allNearbyPlayers = allNearbyPlayers.subList(0, NearModConfig.maxPlayersShown);
            }
            
            cachedNearbyPlayers = allNearbyPlayers;
        } catch (Exception e) {
            NearMod.LOGGER.error("Error updating nearby players", e);
            cachedNearbyPlayers = new ArrayList<>();
        }
    }
    
    /**
     * Renders the overlay on the screen
     */
    private static void renderOverlay(DrawContext drawContext, MinecraftClient client) {
        if (cachedNearbyPlayers.isEmpty()) {
            return;
        }
        
        TextRenderer textRenderer = client.textRenderer;
        int screenWidth = client.getWindow().getScaledWidth();
        
        // Calculate starting position (top-right corner)
        int startY = NearModConfig.offsetY;
        
        // Render header
        String headerText = "Nearby Players (" + cachedNearbyPlayers.size() + ")";
        int headerWidth = textRenderer.getWidth(headerText);
        int headerX = screenWidth - headerWidth - NearModConfig.offsetX;
        
        drawContext.drawTextWithShadow(
            textRenderer,
            headerText,
            headerX,
            startY,
            0xFFFF55 // Yellow color for header
        );
        
        // Render player list
        int currentY = startY + NearModConfig.lineSpacing + 2;
        
        for (PlayerInfo player : cachedNearbyPlayers) {
            String displayText = NearModConfig.showCoordinates ? 
                player.getDetailedText() : 
                player.getDisplayText();
            
            int textWidth = textRenderer.getWidth(displayText);
            int textX = screenWidth - textWidth - NearModConfig.offsetX;
            
            // Choose color based on player status
            int textColor = player.isInvisible ? 0xAAAAAA : NearModConfig.textColor; // Gray for invisible
            
            drawContext.drawTextWithShadow(
                textRenderer,
                displayText,
                textX,
                currentY,
                textColor
            );
            
            currentY += NearModConfig.lineSpacing;
        }
    }
    
    /**
     * Forces an immediate update of the nearby players list
     */
    public static void forceUpdate() {
        updateNearbyPlayers();
        tickCounter = 0;
    }
    
    /**
     * Gets the current cached list of nearby players
     */
    public static List<PlayerInfo> getCachedNearbyPlayers() {
        return new ArrayList<>(cachedNearbyPlayers);
    }
}
