package com.example.near;

/**
 * Configuration for the Near Mod overlay
 */
public class NearModConfig {
    // Overlay settings
    public static boolean overlayEnabled = true;
    public static boolean showCoordinates = false;
    public static boolean showInvisiblePlayers = true;
    public static int maxPlayersShown = 10;
    
    // Position settings
    public static int offsetX = 10;  // Distance from right edge
    public static int offsetY = 10;  // Distance from top edge
    
    // Display settings
    public static int textColor = 0xFFFFFF;      // White
    public static int shadowColor = 0x000000;    // Black
    public static float textScale = 1.0f;
    public static int lineSpacing = 12;
    
    // Update frequency (in ticks)
    public static int updateFrequency = 20; // Update every second (20 ticks)
    
    /**
     * Toggles the overlay on/off
     */
    public static void toggleOverlay() {
        overlayEnabled = !overlayEnabled;
    }
    
    /**
     * Toggles coordinate display
     */
    public static void toggleCoordinates() {
        showCoordinates = !showCoordinates;
    }
    
    /**
     * Toggles invisible player display
     */
    public static void toggleInvisiblePlayers() {
        showInvisiblePlayers = !showInvisiblePlayers;
    }
}
