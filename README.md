# Near Mod для Minecraft 1.21.8

Модифікація для Fabric, яка відображає список поруч знаходящихся гравців у правому верхньому куті екрану.

## Особливості

- **GUI Overlay**: Замість команди `/near`, модифікація тепер показує список гравців безпосередньо на екрані
- **Автоматичне оновлення**: Список гравців оновлюється кожну секунду
- **Налаштування відображення**: Можна перемикати відображення координат
- **Підтримка невидимих гравців**: Показує навіть невидимих гравців (позначені як [Invisible])
- **Радіус пошуку**: 128 блоків навколо гравця

## Керування

- **N** - Увімкнути/вимкнути overlay
- **M** - Увімкнути/вимкнути відображення координат

## Встановлення

1. Встановіть [Fabric Loader](https://fabricmc.net/use/) для Minecraft 1.21.8
2. Встановіть [Fabric API](https://modrinth.com/mod/fabric-api) версії 0.132.0+1.21.8 або новішої
3. Помістіть файл `near-mod-1.0.0.jar` в папку `mods` вашого Minecraft

## Вимоги

- Minecraft 1.21.8
- Fabric Loader 0.17.2+
- Fabric API 0.132.0+1.21.8+
- Java 21+

## Налаштування

Модифікація має наступні налаштування (можна змінити в коді):

- `overlayEnabled` - Увімкнути/вимкнути overlay (за замовчуванням: true)
- `showCoordinates` - Показувати координати (за замовчуванням: false)
- `showInvisiblePlayers` - Показувати невидимих гравців (за замовчуванням: true)
- `maxPlayersShown` - Максимальна кількість гравців для відображення (за замовчуванням: 10)
- `updateFrequency` - Частота оновлення в тіках (за замовчуванням: 20 = 1 секунда)

## Збірка з вихідного коду

```bash
./gradlew build
```

Зібраний файл буде знаходитися в `build/libs/near-mod-1.0.0.jar`

## Ліцензія

CC0-1.0 - Публічне надбання

## Зміни від попередньої версії

- **Оновлено до Minecraft 1.21.8** з Fabric API 0.132.0+1.21.8
- **Видалено команду `/near`** - тепер функціонал працює через GUI overlay
- **Додано постійне відображення** поруч знаходящихся гравців у правому верхньому куті
- **Додано клавіші керування** для швидкого перемикання налаштувань
- **Покращено продуктивність** завдяки кешуванню та періодичному оновленню
