package com.example.near;

import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;

/**
 * Handles key input for the Near Mod
 */
public class KeyInputHandler {
    
    /**
     * Initializes the key input handler
     */
    public static void initialize() {
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            handleKeyInputs(client);
        });
    }
    
    /**
     * Handles key inputs each client tick
     */
    private static void handleKeyInputs(MinecraftClient client) {
        // Handle toggle overlay key
        while (NearMod.toggleOverlayKey.wasPressed()) {
            NearModConfig.toggleOverlay();
            
            if (client.player != null) {
                String message = NearModConfig.overlayEnabled ? 
                    "§aNear Players overlay enabled" : 
                    "§cNear Players overlay disabled";
                client.player.sendMessage(Text.literal(message), false);
            }
            
            // Force update when toggling
            if (NearModConfig.overlayEnabled) {
                NearPlayersOverlay.forceUpdate();
            }
        }
        
        // Handle toggle coordinates key
        while (NearMod.toggleCoordinatesKey.wasPressed()) {
            NearModConfig.toggleCoordinates();
            
            if (client.player != null) {
                String message = NearModConfig.showCoordinates ? 
                    "§aCoordinates display enabled" : 
                    "§cCoordinates display disabled";
                client.player.sendMessage(Text.literal(message), false);
            }
        }
    }
}
